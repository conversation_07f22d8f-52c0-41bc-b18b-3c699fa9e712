#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过wxid请求API获取用户信息并保存到Excel文件
"""

import requests
import pandas as pd
import time
import json
from typing import Dict, List, Optional

class WeixinAccountFetcher:
    def __init__(self):
        self.base_url = "http://game.raisedsun.com/prod-api/weixin-account/list-data"
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w',
            'Content-Length': '0',
            'Origin': 'http://game.raisedsun.com',
            'Proxy-Connection': 'keep-alive',
            'Referer': 'http://game.raisedsun.com/weapp/wx/new-weixin-account',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
        }
        self.cookies = {
            'Admin-Token': 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w'
        }
        
    def fetch_wxid_info(self, wxid: str) -> Optional[Dict]:
        """
        根据wxid获取用户信息
        
        Args:
            wxid (str): 微信ID
            
        Returns:
            Optional[Dict]: 用户信息字典，如果请求失败返回None
        """
        params = {
            'pageNo': 1,
            'pageSize': 20,
            'weixinId': wxid
        }
        
        try:
            response = requests.post(
                self.base_url,
                params=params,
                headers=self.headers,
                cookies=self.cookies,
                verify=False,  # 对应curl的--insecure参数
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"请求失败 {wxid}: HTTP {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常 {wxid}: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析错误 {wxid}: {e}")
            return None
    
    def extract_user_info(self, api_response: Dict, wxid: str) -> Dict:
        """
        从API响应中提取所需的用户信息
        
        Args:
            api_response (Dict): API响应数据
            wxid (str): 微信ID
            
        Returns:
            Dict: 提取的用户信息
        """
        result = {
            'wxid': wxid,
            'nickname': '',
            'accountStatus': '',
            'data62': '',
            'extInfo1': '',
            'extInfo2': ''
        }
        
        try:
            # 检查响应结构
            if 'list' in api_response:
                user_list = api_response['list']
                if user_list and len(user_list) > 0:
                    user_info = user_list[0]  # 取第一个用户信息

                    result['nickname'] = user_info.get('nickname', '')
                    result['accountStatus'] = user_info.get('accountStatus', '')
                    result['data62'] = user_info.get('data62', '')
                    result['extInfo1'] = user_info.get('extInfo1', '')
                    result['extInfo2'] = user_info.get('extInfo2', '')
                else:
                    print(f"未找到用户信息: {wxid}")
            else:
                print(f"响应数据格式异常: {wxid}")
                
        except Exception as e:
            print(f"提取用户信息时发生错误 {wxid}: {e}")
            
        return result
    
    def process_wxid_list(self, wxid_file: str, output_file: str, delay: float = 1.0):
        """
        批量处理wxid列表
        
        Args:
            wxid_file (str): 包含wxid的文件路径
            output_file (str): 输出Excel文件路径
            delay (float): 请求间隔时间（秒）
        """
        results = []
        
        try:
            # 读取wxid文件
            with open(wxid_file, 'r', encoding='utf-8') as f:
                wxids = [line.strip() for line in f if line.strip()]
            
            print(f"开始处理 {len(wxids)} 个wxid...")
            
            for i, wxid in enumerate(wxids, 1):
                print(f"处理进度: {i}/{len(wxids)} - {wxid}")
                
                # 请求API
                api_response = self.fetch_wxid_info(wxid)
                
                if api_response:
                    # 提取用户信息
                    user_info = self.extract_user_info(api_response, wxid)
                    results.append(user_info)
                else:
                    # 请求失败时也添加记录，只包含wxid
                    results.append({
                        'wxid': wxid,
                        'nickname': 'ERROR',
                        'accountStatus': 'ERROR',
                        'data62': 'ERROR',
                        'extInfo1': 'ERROR',
                        'extInfo2': 'ERROR'
                    })
                
                # 添加延时避免请求过于频繁
                if i < len(wxids):
                    time.sleep(delay)
            
            # 保存到Excel
            df = pd.DataFrame(results)
            df.to_excel(output_file, index=False, engine='openpyxl')
            
            print(f"\n处理完成！")
            print(f"总计处理: {len(wxids)} 个wxid")
            print(f"成功获取: {len([r for r in results if r['nickname'] != 'ERROR'])} 个")
            print(f"结果已保存到: {output_file}")
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 {wxid_file}")
        except Exception as e:
            print(f"处理过程中发生错误: {e}")

def test_single_wxid():
    """测试单个wxid"""
    fetcher = WeixinAccountFetcher()
    test_wxid = "wxid_2p5ixbhlii5t12"

    print(f"测试单个wxid: {test_wxid}")
    api_response = fetcher.fetch_wxid_info(test_wxid)

    if api_response:
        print("API响应成功:")
        print(json.dumps(api_response, indent=2, ensure_ascii=False))

        user_info = fetcher.extract_user_info(api_response, test_wxid)
        print("\n提取的用户信息:")
        for key, value in user_info.items():
            print(f"{key}: {value}")
    else:
        print("API请求失败")

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_wxid()
        return

    fetcher = WeixinAccountFetcher()

    # 配置文件路径
    if len(sys.argv) > 1 and sys.argv[1] == "small":
        wxid_file = "test_wxids.txt"  # 测试用的小文件
        output_file = "test_wxid_info.xlsx"  # 测试输出文件
    else:
        wxid_file = "唯一wxid.txt"  # 输入的wxid文件
        output_file = "wxid_info.xlsx"  # 输出的Excel文件

    # 开始处理
    fetcher.process_wxid_list(wxid_file, output_file, delay=1.0)

if __name__ == "__main__":
    main()
