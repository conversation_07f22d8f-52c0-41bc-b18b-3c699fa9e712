# 微信账号信息提取工具

这个工具包含两个主要脚本：

## 1. extract_unique_wxid.py - 提取唯一wxid

从wxid.txt文件中提取唯一的微信ID并保存到新文件。

### 使用方法：
```bash
python3 extract_unique_wxid.py
```

### 功能：
- 读取 `wxid.txt` 文件
- 去除重复的wxid
- 跳过空行
- 按字母顺序排序
- 保存到 `唯一wxid.txt` 文件
- 显示处理统计信息

## 2. fetch_wxid_info.py - 获取wxid详细信息

通过API获取每个wxid的详细信息并保存到Excel文件。

### 使用方法：

#### 测试单个wxid：
```bash
python3 fetch_wxid_info.py test
```

#### 测试小批量（3个wxid）：
```bash
python3 fetch_wxid_info.py small
```

#### 处理完整的唯一wxid文件：
```bash
python3 fetch_wxid_info.py
```

### 功能：
- 从 `唯一wxid.txt` 读取wxid列表
- 通过API获取每个wxid的信息：
  - nickname（昵称）
  - accountStatus（账号状态）
  - data62（data62字段）
  - extInfo1（扩展信息1）
  - extInfo2（扩展信息2）
- 保存结果到 `wxid_info.xlsx` Excel文件
- 请求间隔1秒，避免频繁请求
- 显示处理进度和统计信息

### 输出字段说明：
- **wxid**: 微信ID
- **nickname**: 用户昵称
- **accountStatus**: 账号状态（1=正常）
- **data62**: data62字段值
- **extInfo1**: 扩展信息1
- **extInfo2**: 扩展信息2

### 注意事项：
1. 需要有效的API授权token
2. 请求间隔设置为1秒，可根据需要调整
3. 如果API请求失败，对应字段会显示"ERROR"
4. 脚本会自动处理空值（显示为NaN）

## 依赖包

安装所需的Python包：
```bash
python3 -m pip install requests pandas openpyxl
```

## 文件说明

- `wxid.txt` - 原始wxid文件（包含重复项）
- `唯一wxid.txt` - 去重后的wxid文件
- `test_wxids.txt` - 测试用的小文件（3个wxid）
- `wxid_info.xlsx` - 完整的结果Excel文件
- `test_wxid_info.xlsx` - 测试结果Excel文件
