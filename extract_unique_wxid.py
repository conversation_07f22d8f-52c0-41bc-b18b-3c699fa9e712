#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取wxid.txt中的唯一值并保存到唯一wxid.txt文件
"""

def extract_unique_wxids(input_file, output_file):
    """
    从输入文件中提取唯一的wxid值并保存到输出文件
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
    """
    unique_wxids = set()
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                # 去除行首行尾的空白字符
                wxid = line.strip()
                # 只添加非空的wxid
                if wxid:
                    unique_wxids.add(wxid)
        
        # 将唯一值转换为排序后的列表
        sorted_unique_wxids = sorted(unique_wxids)
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for wxid in sorted_unique_wxids:
                f.write(wxid + '\n')
        
        print(f"处理完成！")
        print(f"原始文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"原始记录数: {sum(1 for line in open(input_file, 'r', encoding='utf-8') if line.strip())}")
        print(f"唯一记录数: {len(sorted_unique_wxids)}")
        print(f"去重后减少了 {sum(1 for line in open(input_file, 'r', encoding='utf-8') if line.strip()) - len(sorted_unique_wxids)} 条重复记录")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

def main():
    """主函数"""
    input_file = "wxid.txt"
    output_file = "唯一wxid.txt"
    
    print("开始提取唯一wxid...")
    extract_unique_wxids(input_file, output_file)

if __name__ == "__main__":
    main()
